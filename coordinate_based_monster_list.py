#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于坐标的怪物列表工具
根据玩家坐标筛选周围怪物
"""

import ctypes
import ctypes.wintypes
import struct
import math

def get_process_handle():
    """获取进程句柄"""
    kernel32 = ctypes.windll.kernel32
    snapshot = kernel32.CreateToolhelp32Snapshot(0x2, 0)

    class PROCESSENTRY32(ctypes.Structure):
        _fields_ = [
            ("dwSize", ctypes.wintypes.DWORD),
            ("cntUsage", ctypes.wintypes.DWORD),
            ("th32ProcessID", ctypes.wintypes.DWORD),
            ("th32DefaultHeapID", ctypes.POINTER(ctypes.wintypes.ULONG)),
            ("th32ModuleID", ctypes.wintypes.DWORD),
            ("cntThreads", ctypes.wintypes.DWORD),
            ("th32ParentProcessID", ctypes.wintypes.DWORD),
            ("pcPriClassBase", ctypes.wintypes.LONG),
            ("dwFlags", ctypes.wintypes.DWORD),
            ("szExeFile", ctypes.c_char * 260)
        ]

    pe32 = PROCESSENTRY32()
    pe32.dwSize = ctypes.sizeof(PROCESSENTRY32)

    process_id = None
    if kernel32.Process32First(snapshot, ctypes.byref(pe32)):
        while True:
            if pe32.szExeFile.decode('utf-8', errors='ignore') == "mts.exe":
                process_id = pe32.th32ProcessID
                break
            if not kernel32.Process32Next(snapshot, ctypes.byref(pe32)):
                break

    kernel32.CloseHandle(snapshot)

    if not process_id:
        return None, None

    handle = kernel32.OpenProcess(0x1F0FFF, False, process_id)
    return handle, process_id

def read_int(handle, address):
    """读取4字节整数"""
    try:
        buffer = ctypes.create_string_buffer(4)
        bytes_read = ctypes.wintypes.DWORD()

        success = ctypes.windll.kernel32.ReadProcessMemory(
            handle, ctypes.c_void_p(address), buffer, 4, ctypes.byref(bytes_read)
        )

        if success and bytes_read.value == 4:
            return struct.unpack('<I', buffer.raw)[0]
        return 0
    except:
        return 0

def read_string_gbk(handle, address, max_length=16):
    """读取GBK编码的字符串"""
    try:
        buffer = ctypes.create_string_buffer(max_length)
        bytes_read = ctypes.wintypes.DWORD()

        success = ctypes.windll.kernel32.ReadProcessMemory(
            handle, ctypes.c_void_p(address), buffer, max_length, ctypes.byref(bytes_read)
        )

        if not success:
            return ""

        data = buffer.raw
        end_pos = data.find(b'\x00')
        if end_pos != -1:
            data = data[:end_pos]

        try:
            return data.decode('gbk', errors='ignore').strip()
        except:
            try:
                return data.decode('utf-8', errors='ignore').strip()
            except:
                return data.decode('ascii', errors='ignore').strip()
    except:
        return ""

def read_object_info(handle, obj_addr):
    """读取对象详细信息"""
    info = {
        'name': '',
        'hp': 0,
        'type': 0,
        'x': 0,
        'y': 0,
        'valid': False
    }

    try:
        if obj_addr < 0x10000000 or obj_addr > 0x7FFFFFFF:
            return info

        # 读取名字 (偏移 0x2C, 16字节, GBK编码)
        info['name'] = read_string_gbk(handle, obj_addr + 0x2C, 16)

        # 读取血量 (偏移 0x5D, 1字节)
        hp_value = read_int(handle, obj_addr + 0x5D)
        info['hp'] = hp_value & 0xFF

        # 读取类型 (偏移 1280)
        type_value = read_int(handle, obj_addr + 1280)
        info['type'] = type_value & 0xFF

        # 读取坐标 (偏移 1204, 1208)
        info['x'] = read_int(handle, obj_addr + 1204)
        info['y'] = read_int(handle, obj_addr + 1208)

        info['valid'] = True

    except:
        pass

    return info

def get_player_info(handle):
    """获取玩家信息"""
    NEARBY_ARRAY = 0x005297F8

    # 检查前几个索引，寻找真正的玩家
    for i in range(5):  # 检查索引0-4
        player_addr = read_int(handle, NEARBY_ARRAY + (i * 4))

        if player_addr and 0x10000000 <= player_addr <= 0x7FFFFFFF:
            player_info = read_object_info(handle, player_addr)
            if player_info['valid']:
                # 优先选择有名字且坐标不为(0,0)的对象
                if player_info['name'] and (player_info['x'] != 0 or player_info['y'] != 0):
                    print(f"🎯 找到玩家: 索引{i} - {player_info['name']} 坐标({player_info['x']}, {player_info['y']})")
                    return player_info
                # 如果没有更好的选择，记录第一个有效对象
                elif i == 0:
                    first_valid = player_info

    # 如果没找到有名字的，返回第一个有效的
    if 'first_valid' in locals():
        print(f"🎯 使用第一个有效对象作为玩家基准")
        return first_valid

    return None

def calculate_distance(x1, y1, x2, y2):
    """计算两点间距离"""
    return math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

def get_object_type(index, monster_type, name):
    """判断对象类型"""
    if index == 0:
        return "玩家对象"
    elif index == 1:
        return "玩家相关"
    elif monster_type <= 1:
        return "其他对象"
    elif name and len(name) > 0:
        return "怪物对象"
    else:
        return "未知对象"

def refresh_nearby_monsters_with_coordinates():
    """基于坐标刷新周围怪物"""
    print("🎯 基于坐标的怪物列表工具")
    print("=" * 70)
    
    handle, pid = get_process_handle()
    if not handle:
        print("❌ 无法连接到游戏进程")
        return
    
    try:
        print(f"✅ 连接到进程 mts.exe (PID: {pid})")
        
        # 获取玩家信息
        print("\n👤 获取玩家信息...")
        player_info = get_player_info(handle)
        
        if not player_info:
            print("❌ 无法获取玩家信息")
            return
        
        print(f"玩家坐标: ({player_info['x']}, {player_info['y']})")
        print(f"玩家名字: {player_info['name']}")
        print()
        
        # 扫描周围怪物数组
        NEARBY_ARRAY = 0x005297F8
        print("🔍 扫描周围怪物数组...")
        print("索引  地址        类型        名字        血量  坐标            距离")
        print("-" * 80)
        
        nearby_monsters = []
        
        for i in range(50):  # 扫描前50个位置
            element_addr = NEARBY_ARRAY + (i * 4)
            obj_addr = read_int(handle, element_addr)
            
            if obj_addr and 0x10000000 <= obj_addr <= 0x7FFFFFFF:
                obj_info = read_object_info(handle, obj_addr)
                
                if obj_info['valid']:
                    # 计算与玩家的距离
                    distance = calculate_distance(
                        player_info['x'], player_info['y'],
                        obj_info['x'], obj_info['y']
                    )
                    
                    obj_type = get_object_type(i, obj_info['type'], obj_info['name'])
                    name_display = obj_info['name'][:10] if obj_info['name'] else ""
                    coord_str = f"({obj_info['x']:4d},{obj_info['y']:4d})"
                    
                    print(f"{i:2d}    0x{obj_addr:08X}  {obj_type:10s}  {name_display:10s}  {obj_info['hp']:3d}  {coord_str:12s}  {distance:6.1f}")
                    
                    # 收集怪物信息
                    if obj_type == "怪物对象" and distance > 0:  # 排除玩家自己
                        nearby_monsters.append({
                            'index': i,
                            'address': obj_addr,
                            'info': obj_info,
                            'distance': distance
                        })
        
        print(f"\n📊 找到 {len(nearby_monsters)} 个怪物对象")
        
        if nearby_monsters:
            # 按距离排序
            nearby_monsters.sort(key=lambda x: x['distance'])
            
            print("\n🎯 按距离排序的怪物列表:")
            print("排名  索引  名字        距离    坐标")
            print("-" * 50)
            
            for rank, monster in enumerate(nearby_monsters[:10], 1):  # 显示最近的10个
                info = monster['info']
                name = info['name'] if info['name'] else "无名"
                coord_str = f"({info['x']},{info['y']})"
                
                print(f"{rank:2d}    {monster['index']:2d}    {name:10s}  {monster['distance']:6.1f}  {coord_str}")
        
        # 检查可击杀列表
        print("\n⚔️ 检查可击杀列表...")
        KILLABLE_ARRAY = 0x005293F8
        killable_count = 0
        
        for i in range(20):
            element_addr = KILLABLE_ARRAY + (i * 4)
            obj_addr = read_int(handle, element_addr)
            
            if obj_addr and 0x10000000 <= obj_addr <= 0x7FFFFFFF:
                obj_info = read_object_info(handle, obj_addr)
                if obj_info['valid'] and obj_info['name']:
                    killable_count += 1
        
        print(f"当前可击杀列表中有 {killable_count} 个对象")
        
        # 显示当前选中目标
        selected_ptr_addr = 0x400000 + 0x64C0E8
        selected_target = read_int(handle, selected_ptr_addr)
        print(f"当前选中目标: 0x{selected_target:08X}")
        
        if selected_target and 0x10000000 <= selected_target <= 0x7FFFFFFF:
            selected_info = read_object_info(handle, selected_target)
            if selected_info['valid']:
                distance_to_selected = calculate_distance(
                    player_info['x'], player_info['y'],
                    selected_info['x'], selected_info['y']
                )
                print(f"选中目标: {selected_info['name']} 距离: {distance_to_selected:.1f}")
        
        print("\n💡 操作建议:")
        if nearby_monsters:
            print("1. 游戏中有怪物，可以手动点击选择")
            print("2. 选择最近的怪物进行攻击")
        else:
            print("1. 当前位置没有怪物")
            print("2. 移动到有怪物的区域")
        
    finally:
        ctypes.windll.kernel32.CloseHandle(handle)

if __name__ == "__main__":
    refresh_nearby_monsters_with_coordinates()
