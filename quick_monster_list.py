#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速怪物列表生成器
直接输出像图片中那样的简洁怪物列表
"""

import ctypes
import ctypes.wintypes
import struct

def enable_debug_privilege():
    """启用调试权限"""
    try:
        token = ctypes.wintypes.HANDLE()
        advapi32 = ctypes.windll.advapi32
        kernel32 = ctypes.windll.kernel32

        if not advapi32.OpenProcessToken(
            kernel32.GetCurrentProcess(),
            0x00000020 | 0x00000008,
            ctypes.byref(token)
        ):
            return False

        class LUID(ctypes.Structure):
            _fields_ = [("LowPart", ctypes.wintypes.DWORD),
                       ("HighPart", ctypes.wintypes.LONG)]

        luid = LUID()
        if not advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", ctypes.byref(luid)):
            return False

        class TOKEN_PRIVILEGES(ctypes.Structure):
            _fields_ = [("PrivilegeCount", ctypes.wintypes.DWORD),
                       ("Luid", LUID),
                       ("Attributes", ctypes.wintypes.DWORD)]

        tp = TOKEN_PRIVILEGES()
        tp.PrivilegeCount = 1
        tp.Luid = luid
        tp.Attributes = 0x00000002

        result = advapi32.AdjustTokenPrivileges(token, False, ctypes.byref(tp), 0, None, None)
        kernel32.CloseHandle(token)
        return result != 0
    except:
        return False

def get_process_handle(process_name="mts.exe"):
    """获取进程句柄"""
    # 启用调试权限
    enable_debug_privilege()

    kernel32 = ctypes.windll.kernel32
    snapshot = kernel32.CreateToolhelp32Snapshot(0x2, 0)

    class PROCESSENTRY32(ctypes.Structure):
        _fields_ = [
            ("dwSize", ctypes.wintypes.DWORD),
            ("cntUsage", ctypes.wintypes.DWORD),
            ("th32ProcessID", ctypes.wintypes.DWORD),
            ("th32DefaultHeapID", ctypes.POINTER(ctypes.wintypes.ULONG)),
            ("th32ModuleID", ctypes.wintypes.DWORD),
            ("cntThreads", ctypes.wintypes.DWORD),
            ("th32ParentProcessID", ctypes.wintypes.DWORD),
            ("pcPriClassBase", ctypes.wintypes.LONG),
            ("dwFlags", ctypes.wintypes.DWORD),
            ("szExeFile", ctypes.c_char * 260)
        ]

    pe32 = PROCESSENTRY32()
    pe32.dwSize = ctypes.sizeof(PROCESSENTRY32)

    process_id = None
    if kernel32.Process32First(snapshot, ctypes.byref(pe32)):
        while True:
            if pe32.szExeFile.decode('utf-8', errors='ignore') == process_name:
                process_id = pe32.th32ProcessID
                break
            if not kernel32.Process32Next(snapshot, ctypes.byref(pe32)):
                break

    kernel32.CloseHandle(snapshot)

    if not process_id:
        return None, None

    handle = kernel32.OpenProcess(0x1F0FFF, False, process_id)
    return handle, process_id

def read_int(handle, address):
    """读取4字节整数"""
    buffer = ctypes.create_string_buffer(4)
    bytes_read = ctypes.wintypes.DWORD()
    
    success = ctypes.windll.kernel32.ReadProcessMemory(
        handle, ctypes.c_void_p(address), buffer, 4, ctypes.byref(bytes_read)
    )
    
    return struct.unpack('<I', buffer.raw)[0] if success else 0

def read_byte(handle, address):
    """读取1字节"""
    buffer = ctypes.create_string_buffer(1)
    bytes_read = ctypes.wintypes.DWORD()

    success = ctypes.windll.kernel32.ReadProcessMemory(
        handle, ctypes.c_void_p(address), buffer, 1, ctypes.byref(bytes_read)
    )

    return struct.unpack('<B', buffer.raw)[0] if success else 0

def read_string_gbk(handle, address, max_length=16):
    """读取GBK编码的字符串"""
    try:
        buffer = ctypes.create_string_buffer(max_length)
        bytes_read = ctypes.wintypes.DWORD()

        success = ctypes.windll.kernel32.ReadProcessMemory(
            handle, ctypes.c_void_p(address), buffer, max_length, ctypes.byref(bytes_read)
        )

        if not success:
            return ""

        # 查找字符串结束符
        data = buffer.raw
        end_pos = data.find(b'\x00')
        if end_pos != -1:
            data = data[:end_pos]

        # 尝试GBK解码
        try:
            return data.decode('gbk', errors='ignore').strip()
        except:
            try:
                return data.decode('utf-8', errors='ignore').strip()
            except:
                return data.decode('ascii', errors='ignore').strip()
    except:
        return ""

def read_monster_info(handle, monster_addr):
    """读取怪物详细信息"""
    info = {
        'name': '',
        'hp': 0,
        'type': 0,
        'x': 0,
        'y': 0
    }

    try:
        # 读取名字 (偏移 0x2C, 16字节, GBK编码)
        info['name'] = read_string_gbk(handle, monster_addr + 0x2C, 16)

        # 读取血量 (偏移 0x5D, 1字节)
        success, hp_value = test_memory_access(handle, monster_addr + 0x5D)
        if success:
            info['hp'] = hp_value & 0xFF

        # 读取类型 (偏移 1280)
        success, type_value = test_memory_access(handle, monster_addr + 1280)
        if success:
            info['type'] = type_value & 0xFF

        # 读取坐标
        x_success, x = test_memory_access(handle, monster_addr + 1204)
        y_success, y = test_memory_access(handle, monster_addr + 1208)
        if x_success and y_success:
            info['x'] = x
            info['y'] = y

    except:
        pass

    return info

def get_object_type(index, monster_type):
    """判断对象类型"""
    if index == 0:
        return "玩家对象"
    elif index == 1:
        return "玩家相关对象"
    elif monster_type <= 1:
        return "其他对象"
    else:
        return "怪物对象"

def get_current_selected_index(handle):
    """获取当前选中的怪物索引"""
    BASE_ADDRESS = 0x400000
    SELECTED_TARGET_PTR = 0x64C0E8
    MONSTER_ARRAY_BASE = BASE_ADDRESS + 0x5297F8

    selected_addr = read_int(handle, BASE_ADDRESS + SELECTED_TARGET_PTR)
    if selected_addr == 0:
        return None

    # 在数组中查找这个地址
    for i in range(50):  # 只检查前50个
        element_addr = MONSTER_ARRAY_BASE + (i * 4)
        monster_addr = read_int(handle, element_addr)
        if monster_addr == selected_addr:
            return i
    return None

def get_current_selected_index_new(handle, array_addr):
    """获取当前选中的怪物索引（新版本）"""
    # 尝试多个可能的选中目标指针地址
    possible_ptrs = [
        0x400000 + 0x64C0E8,
        0x400000 + 0x64C0E4,
        0x400000 + 0x64C0EC,
    ]

    for ptr_addr in possible_ptrs:
        success, selected_addr = test_memory_access(handle, ptr_addr)
        if success and selected_addr != 0:
            # 在数组中查找这个地址
            for i in range(50):
                element_addr = array_addr + (i * 4)
                success2, monster_addr = test_memory_access(handle, element_addr)
                if success2 and monster_addr == selected_addr:
                    return i
    return None

def test_memory_access(handle, address):
    """测试内存访问"""
    try:
        buffer = ctypes.create_string_buffer(4)
        bytes_read = ctypes.wintypes.DWORD()

        success = ctypes.windll.kernel32.ReadProcessMemory(
            handle, ctypes.c_void_p(address), buffer, 4, ctypes.byref(bytes_read)
        )

        if success and bytes_read.value == 4:
            return True, struct.unpack('<I', buffer.raw)[0]
        return False, 0
    except:
        return False, 0

def find_monster_array(handle):
    """查找怪物数组"""
    # 可能的地址组合
    possible_addresses = [
        0x400000 + 0x5297F8,  # 原始地址
        0x400000 + 0x1297F8,  # 可能变化
        0x400000 + 0x4297F8,  # 可能变化
        0x400000 + 0x6297F8,  # 可能变化
        0x10000000 + 0x5297F8, # 高基址
    ]

    for addr in possible_addresses:
        success, value = test_memory_access(handle, addr)
        if success and value != 0:
            # 检查是否像指针数组
            valid_count = 0
            for i in range(10):
                success2, value2 = test_memory_access(handle, addr + i * 4)
                if success2 and 0x10000000 <= value2 <= 0x7FFFFFFF:
                    valid_count += 1

            if valid_count >= 3:
                return addr

    return None

def generate_monster_list():
    """生成怪物列表"""
    # 连接进程
    handle, _ = get_process_handle()
    if not handle:
        print("❌ 错误: 无法连接到游戏进程 mts.exe")
        print("请确保:")
        print("1. 游戏正在运行")
        print("2. 以管理员权限运行此脚本")
        return
    
    try:
        # 查找怪物数组
        print("🔍 正在查找怪物数组...")
        array_addr = find_monster_array(handle)

        if not array_addr:
            print("❌ 未找到有效的怪物数组")
            print("可能的原因:")
            print("1. 游戏版本不匹配")
            print("2. 需要进入游戏场景")
            print("3. 地址已发生变化")
            return

        print(f"✅ 找到怪物数组: 0x{array_addr:08X}")
        print()
        print("怪物对象地址列表:")
        print()

        # 获取当前选中的怪物
        selected_index = get_current_selected_index_new(handle, array_addr)

        # 扫描怪物数组
        monster_count = 0
        for i in range(50):  # 扫描前50个位置
            # 计算数组元素地址
            element_addr = array_addr + (i * 4)

            # 读取怪物对象地址
            success, monster_addr = test_memory_access(handle, element_addr)

            if success and monster_addr != 0:
                # 读取怪物详细信息
                monster_info = read_monster_info(handle, monster_addr)

                # 判断对象类型
                obj_type = get_object_type(i, monster_info['type'])

                # 检查是否是选中的怪物
                selected_mark = " ✅" if i == selected_index else ""

                # 格式化名字显示
                name_display = monster_info['name'][:12] if monster_info['name'] else ""

                # 输出格式化结果
                print(f"{i:2d}    0x{monster_addr:08X}  {obj_type}{selected_mark}")
                if name_display or monster_info['hp'] > 0:
                    print(f"      名字: {name_display}  血量: {monster_info['hp']}")

                monster_count += 1

        if monster_count >= 50:
            print("还有更多...")

        print()
        print(f"📊 找到 {monster_count} 个活跃对象")

        if selected_index is not None:
            success, selected_addr = test_memory_access(handle, array_addr + (selected_index * 4))
            if success:
                print(f"🎯 当前选中: 索引{selected_index} (0x{selected_addr:08X})")
        else:
            print("🎯 当前没有选中目标")
            
    finally:
        ctypes.windll.kernel32.CloseHandle(handle)

def generate_with_coordinates():
    """生成带坐标的怪物列表"""
    handle, _ = get_process_handle()
    if not handle:
        print("❌ 无法连接到游戏进程")
        return

    try:
        # 查找怪物数组
        array_addr = find_monster_array(handle)
        if not array_addr:
            print("❌ 未找到有效的怪物数组")
            return

        print("🔍 怪物数组详细分析")
        print()
        print("索引  地址        类型        坐标      名字        血量")
        print("-" * 80)

        selected_index = get_current_selected_index_new(handle, array_addr)

        for i in range(30):
            element_addr = array_addr + (i * 4)
            success, monster_addr = test_memory_access(handle, element_addr)

            if success and monster_addr != 0:
                # 读取怪物详细信息
                monster_info = read_monster_info(handle, monster_addr)

                # 判断对象类型
                obj_type = get_object_type(i, monster_info['type'])

                # 格式化坐标
                if monster_info['x'] != 0 or monster_info['y'] != 0:
                    coord_str = f"({monster_info['x']:3d},{monster_info['y']:3d})"
                else:
                    coord_str = "(?,?)"

                # 格式化名字
                name_display = monster_info['name'][:10] if monster_info['name'] else ""

                selected_mark = " ✅" if i == selected_index else ""

                print(f"{i:2d}    0x{monster_addr:08X}  {obj_type:10s}  {coord_str:8s}  {name_display:10s}  {monster_info['hp']:3d}{selected_mark}")

    finally:
        ctypes.windll.kernel32.CloseHandle(handle)

def show_calculation_details():
    """显示地址计算详情"""
    handle, _ = get_process_handle()
    if not handle:
        print("❌ 无法连接到游戏进程")
        return

    try:
        # 查找怪物数组
        array_addr = find_monster_array(handle)
        if not array_addr:
            print("❌ 未找到有效的怪物数组")
            return

        print("🧮 地址计算详解")
        print("=" * 60)
        print(f"找到的数组地址: 0x{array_addr:08X}")
        print()
        print("计算公式: 数组基地址 + (索引 × 4)")
        print()
        print("索引  数组地址    存储的对象地址  计算过程")
        print("-" * 70)

        for i in range(15):
            element_addr = array_addr + (i * 4)
            success, monster_addr = test_memory_access(handle, element_addr)

            if success and monster_addr != 0:
                print(f"{i:2d}    0x{element_addr:08X}  0x{monster_addr:08X}      "
                      f"0x{array_addr:08X} + ({i} × 4)")
            else:
                print(f"{i:2d}    0x{element_addr:08X}  (空)              "
                      f"0x{array_addr:08X} + ({i} × 4)")

    finally:
        ctypes.windll.kernel32.CloseHandle(handle)

def generate_detailed_monster_info():
    """生成超详细的怪物信息"""
    handle, _ = get_process_handle()
    if not handle:
        print("❌ 无法连接到游戏进程")
        return

    try:
        # 查找怪物数组
        array_addr = find_monster_array(handle)
        if not array_addr:
            print("❌ 未找到有效的怪物数组")
            return

        print("🔍 怪物详细信息分析")
        print("=" * 100)

        selected_index = get_current_selected_index_new(handle, array_addr)

        monster_count = 0
        for i in range(50):
            element_addr = array_addr + (i * 4)
            success, monster_addr = test_memory_access(handle, element_addr)

            if success and monster_addr != 0:
                monster_info = read_monster_info(handle, monster_addr)
                obj_type = get_object_type(i, monster_info['type'])

                selected_mark = " 🎯选中" if i == selected_index else ""

                print(f"\n【索引 {i:2d}】 {obj_type}{selected_mark}")
                print(f"  地址: 0x{monster_addr:08X}")
                print(f"  名字: {monster_info['name'] if monster_info['name'] else '(无名)'}")
                print(f"  血量: {monster_info['hp']}")
                print(f"  类型: {monster_info['type']}")
                print(f"  坐标: ({monster_info['x']}, {monster_info['y']})")

                # 显示原始数据
                print(f"  原始数据:")
                print(f"    名字地址: 0x{monster_addr + 0x2C:08X}")
                print(f"    血量地址: 0x{monster_addr + 0x5D:08X}")
                print(f"    类型地址: 0x{monster_addr + 1280:08X}")
                print(f"    坐标地址: 0x{monster_addr + 1204:08X}, 0x{monster_addr + 1208:08X}")

                monster_count += 1

                if monster_count >= 20:  # 限制显示数量
                    print(f"\n... 还有更多怪物 (已显示前{monster_count}个)")
                    break

        print(f"\n📊 总计找到 {monster_count} 个活跃对象")

    finally:
        ctypes.windll.kernel32.CloseHandle(handle)

def main():
    """主函数"""
    print("🎮 快速怪物列表生成器")
    print("=" * 40)
    
    while True:
        print("\n选择功能:")
        print("1. 生成怪物地址列表 (简洁版)")
        print("2. 生成带坐标的详细列表")
        print("3. 生成超详细怪物信息 (包含名字、血量)")
        print("4. 显示地址计算详情")
        print("0. 退出")

        choice = input("\n请选择 (0-4): ").strip()

        if choice == "1":
            generate_monster_list()
        elif choice == "2":
            generate_with_coordinates()
        elif choice == "3":
            generate_detailed_monster_info()
        elif choice == "4":
            show_calculation_details()
        elif choice == "0":
            print("👋 退出程序")
            break
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
