#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标移动工具
输入目标坐标，自动移动到指定位置
"""

import ctypes
import ctypes.wintypes
import struct
import time
import math

def enable_debug_privilege():
    """启用调试权限"""
    try:
        token = ctypes.wintypes.HANDLE()
        advapi32 = ctypes.windll.advapi32
        kernel32 = ctypes.windll.kernel32

        if not advapi32.OpenProcessToken(
            kernel32.GetCurrentProcess(),
            0x00000020 | 0x00000008,
            ctypes.byref(token)
        ):
            return False

        class LUID(ctypes.Structure):
            _fields_ = [("LowPart", ctypes.wintypes.DWORD),
                       ("HighPart", ctypes.wintypes.LONG)]

        luid = LUID()
        if not advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", ctypes.byref(luid)):
            return False

        class TOKEN_PRIVILEGES(ctypes.Structure):
            _fields_ = [("PrivilegeCount", ctypes.wintypes.DWORD),
                       ("Luid", LUID),
                       ("Attributes", ctypes.wintypes.DWORD)]

        tp = TOKEN_PRIVILEGES()
        tp.PrivilegeCount = 1
        tp.Luid = luid
        tp.Attributes = 0x00000002

        result = advapi32.AdjustTokenPrivileges(token, False, ctypes.byref(tp), 0, None, None)
        kernel32.CloseHandle(token)
        return result != 0
    except:
        return False

def get_process_handle():
    """获取进程句柄"""
    enable_debug_privilege()
    
    kernel32 = ctypes.windll.kernel32
    snapshot = kernel32.CreateToolhelp32Snapshot(0x2, 0)

    class PROCESSENTRY32(ctypes.Structure):
        _fields_ = [
            ("dwSize", ctypes.wintypes.DWORD),
            ("cntUsage", ctypes.wintypes.DWORD),
            ("th32ProcessID", ctypes.wintypes.DWORD),
            ("th32DefaultHeapID", ctypes.POINTER(ctypes.wintypes.ULONG)),
            ("th32ModuleID", ctypes.wintypes.DWORD),
            ("cntThreads", ctypes.wintypes.DWORD),
            ("th32ParentProcessID", ctypes.wintypes.DWORD),
            ("pcPriClassBase", ctypes.wintypes.LONG),
            ("dwFlags", ctypes.wintypes.DWORD),
            ("szExeFile", ctypes.c_char * 260)
        ]

    pe32 = PROCESSENTRY32()
    pe32.dwSize = ctypes.sizeof(PROCESSENTRY32)

    process_id = None
    if kernel32.Process32First(snapshot, ctypes.byref(pe32)):
        while True:
            if pe32.szExeFile.decode('utf-8', errors='ignore') == "mts.exe":
                process_id = pe32.th32ProcessID
                break
            if not kernel32.Process32Next(snapshot, ctypes.byref(pe32)):
                break

    kernel32.CloseHandle(snapshot)

    if not process_id:
        return None, None

    handle = kernel32.OpenProcess(0x1F0FFF, False, process_id)
    return handle, process_id

def read_int(handle, address):
    """读取4字节整数"""
    try:
        buffer = ctypes.create_string_buffer(4)
        bytes_read = ctypes.wintypes.DWORD()

        success = ctypes.windll.kernel32.ReadProcessMemory(
            handle, ctypes.c_void_p(address), buffer, 4, ctypes.byref(bytes_read)
        )

        if success and bytes_read.value == 4:
            return struct.unpack('<I', buffer.raw)[0]
        return 0
    except:
        return 0

def read_byte(handle, address):
    """读取1字节"""
    try:
        buffer = ctypes.create_string_buffer(1)
        bytes_read = ctypes.wintypes.DWORD()

        success = ctypes.windll.kernel32.ReadProcessMemory(
            handle, ctypes.c_void_p(address), buffer, 1, ctypes.byref(bytes_read)
        )

        if success and bytes_read.value == 1:
            return buffer.raw[0]
        return 0
    except:
        return 0

def write_int(handle, address, value):
    """写入4字节整数"""
    try:
        buffer = struct.pack('<I', value)
        bytes_written = ctypes.wintypes.DWORD()

        success = ctypes.windll.kernel32.WriteProcessMemory(
            handle, ctypes.c_void_p(address), buffer, 4, ctypes.byref(bytes_written)
        )

        return success and bytes_written.value == 4
    except:
        return False

def call_remote_function(handle, func_addr, param):
    """调用远程函数"""
    try:
        # 分配远程内存用于参数
        kernel32 = ctypes.windll.kernel32

        # 分配内存
        remote_mem = kernel32.VirtualAllocEx(
            handle, None, 1024, 0x1000 | 0x2000, 0x40  # MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE
        )

        if not remote_mem:
            return False

        try:
            # 创建简单的调用代码
            # push param
            # call func_addr
            # ret
            shellcode = bytearray([
                0x68,  # push imm32
                *struct.pack('<I', param),  # 参数
                0xE8,  # call rel32
                *struct.pack('<i', func_addr - (remote_mem + 10)),  # 相对地址
                0xC3   # ret
            ])

            # 写入shellcode
            bytes_written = ctypes.wintypes.DWORD()
            success = kernel32.WriteProcessMemory(
                handle, ctypes.c_void_p(remote_mem), bytes(shellcode), len(shellcode), ctypes.byref(bytes_written)
            )

            if not success:
                return False

            # 创建远程线程执行
            thread_id = ctypes.wintypes.DWORD()
            thread_handle = kernel32.CreateRemoteThread(
                handle, None, 0, remote_mem, None, 0, ctypes.byref(thread_id)
            )

            if thread_handle:
                # 等待执行完成
                kernel32.WaitForSingleObject(thread_handle, 5000)  # 5秒超时
                kernel32.CloseHandle(thread_handle)
                return True

            return False

        finally:
            # 释放内存
            kernel32.VirtualFreeEx(handle, remote_mem, 0, 0x8000)  # MEM_RELEASE

    except Exception as e:
        print(f"调用远程函数失败: {e}")
        return False

def get_movement_control_address(handle):
    """获取移动控制基地址"""
    BASE_ADDRESS = 0x400000
    MOVEMENT_PTR_OFFSET = 0x1297FC

    # 读取 [mts.exe+1297FC] 的值
    movement_ptr_addr = BASE_ADDRESS + MOVEMENT_PTR_OFFSET
    movement_base = read_int(handle, movement_ptr_addr)

    return movement_base

def get_current_coordinates(handle):
    """获取当前真实坐标"""
    # 使用正确的坐标地址：[mts.exe+1297FC]+0x4A4 和 +0x4A8
    movement_base = get_movement_control_address(handle)
    if not movement_base:
        return 0, 0

    x = read_int(handle, movement_base + 0x4A4)  # 真实X坐标
    y = read_int(handle, movement_base + 0x4A8)  # 真实Y坐标

    return x, y

def get_target_coordinates(handle):
    """获取目标坐标 (安全模式)"""
    # 使用安全模式坐标地址：[mts.exe+1297FC]+0x4C4 和 +0x4C8
    movement_base = get_movement_control_address(handle)
    if not movement_base:
        return 0, 0

    x = read_int(handle, movement_base + 0x4C4)  # 目标X坐标
    y = read_int(handle, movement_base + 0x4C8)  # 目标Y坐标

    return x, y

def set_target_coordinates(handle, target_x, target_y):
    """设置目标坐标 (安全模式)"""
    movement_base = get_movement_control_address(handle)
    if not movement_base:
        print("❌ 无法获取移动控制基地址")
        return False

    print(f"📍 移动控制基地址: 0x{movement_base:08X}")

    # 设置目标坐标
    success_x = write_int(handle, movement_base + 0x4C4, target_x)
    success_y = write_int(handle, movement_base + 0x4C8, target_y)

    if success_x and success_y:
        print(f"✅ 目标坐标设置成功: ({target_x}, {target_y})")
        return True
    else:
        print("❌ 目标坐标设置失败")
        return False

def calculate_direction(from_x, from_y, to_x, to_y):
    """计算移动方向 (0-19)"""
    dx = to_x - from_x
    dy = to_y - from_y

    if dx == 0 and dy == 0:
        return 0

    # 计算角度 (弧度)
    angle = math.atan2(dy, dx)

    # 转换为度数
    degrees = math.degrees(angle)

    # 调整为0-360度
    if degrees < 0:
        degrees += 360

    # 转换为游戏的20个方向 (0-19)
    direction = int((degrees + 9) / 18) % 20

    return direction

def get_player_object_address(handle):
    """获取玩家对象地址"""
    # 从怪物数组中获取玩家对象地址
    NEARBY_ARRAY = 0x005297F8

    # 检查索引1 (通常是玩家相关对象)
    player_addr = read_int(handle, NEARBY_ARRAY + 4)  # 索引1

    if player_addr and 0x10000000 <= player_addr <= 0x7FFFFFFF:
        return player_addr

    # 如果索引1失败，尝试索引0
    player_addr = read_int(handle, NEARBY_ARRAY)  # 索引0

    if player_addr and 0x10000000 <= player_addr <= 0x7FFFFFFF:
        return player_addr

    return None

def call_movement_function(handle, player_obj_addr, target_x, target_y, direction):
    """调用游戏的移动函数"""
    try:
        # 移动Call地址
        MOVEMENT_CALL = 0x004218B0

        print(f"🎮 调用移动函数:")
        print(f"   函数地址: 0x{MOVEMENT_CALL:08X}")
        print(f"   玩家对象: 0x{player_obj_addr:08X}")
        print(f"   目标坐标: ({target_x}, {target_y})")
        print(f"   方向: {direction}")

        # 方法A: 直接设置玩家对象的目标坐标
        # 根据IDA分析，偏移1220和1224存储目标坐标
        success1 = write_int(handle, player_obj_addr + 1220, target_x)
        success2 = write_int(handle, player_obj_addr + 1224, target_y)

        if success1 and success2:
            print("✅ 目标坐标设置成功")

            # 设置移动状态标志
            write_int(handle, player_obj_addr + 1292, 1)  # 移动标志
            write_int(handle, player_obj_addr + 1293, direction)  # 方向
            write_int(handle, player_obj_addr + 1294, 1)  # 移动类型

            print("✅ 移动状态设置完成")
            return True
        else:
            print("❌ 目标坐标设置失败")

        # 方法B: 构造移动命令包 (更复杂但更安全)
        print("🔄 尝试构造移动命令包...")

        # 分配内存用于命令数据
        kernel32 = ctypes.windll.kernel32

        # 构造移动命令数据 (基于IDA分析的格式)
        command_data = bytearray(16)
        command_data[0] = 1  # 命令类型 (1 = 移动)
        struct.pack_into('<H', command_data, 1, target_x)  # X坐标
        struct.pack_into('<H', command_data, 3, target_y)  # Y坐标
        command_data[5] = (direction << 3) & 0xFF  # 方向信息

        # 分配远程内存
        remote_mem = kernel32.VirtualAllocEx(
            handle, None, len(command_data), 0x1000 | 0x2000, 0x04  # MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE
        )

        if remote_mem:
            try:
                # 写入命令数据
                bytes_written = ctypes.wintypes.DWORD()
                success = kernel32.WriteProcessMemory(
                    handle, ctypes.c_void_p(remote_mem), bytes(command_data), len(command_data), ctypes.byref(bytes_written)
                )

                if success:
                    # 设置玩家对象的命令指针 (偏移976)
                    write_int(handle, player_obj_addr + 976, remote_mem)
                    print("✅ 移动命令包设置成功")
                    return True

            finally:
                # 延迟释放内存，让游戏有时间处理
                time.sleep(0.1)
                kernel32.VirtualFreeEx(handle, remote_mem, 0, 0x8000)

        return False

    except Exception as e:
        print(f"❌ 移动函数调用异常: {e}")
        return False

def move_to_coordinates(handle, target_x, target_y):
    """移动到指定坐标"""
    print(f"🎯 开始移动到坐标 ({target_x}, {target_y})")
    
    # 获取当前坐标
    current_x, current_y = get_current_coordinates(handle)
    print(f"📍 当前坐标: ({current_x}, {current_y})")
    
    if current_x == target_x and current_y == target_y:
        print("✅ 已经在目标位置!")
        return True
    
    # 计算距离
    distance = math.sqrt((target_x - current_x) ** 2 + (target_y - current_y) ** 2)
    print(f"📏 移动距离: {distance:.1f}")
    
    # 计算方向
    direction = calculate_direction(current_x, current_y, target_x, target_y)
    print(f"🧭 移动方向: {direction} (0-19)")
    
    # 方法1: 直接修改坐标 (瞬移)
    print(f"\n🚀 方法1: 直接坐标修改")
    
    PLAYER_X_ADDR = 0x3EDB14AC
    PLAYER_Y_ADDR = 0x3EDB14B0
    
    success_x = write_int(handle, PLAYER_X_ADDR, target_x)
    success_y = write_int(handle, PLAYER_Y_ADDR, target_y)
    
    if success_x and success_y:
        print("✅ 坐标修改成功!")
        
        # 验证坐标
        time.sleep(0.1)
        new_x, new_y = get_current_coordinates(handle)
        print(f"📍 新坐标: ({new_x}, {new_y})")
        
        if new_x == target_x and new_y == target_y:
            print("🎉 移动成功!")
            return True
        else:
            print("⚠️ 坐标可能被游戏重置，尝试其他方法...")
    else:
        print("❌ 坐标修改失败")
    
    # 方法2: 调用移动函数 (如果直接修改失败)
    print(f"\n🎮 方法2: 调用移动函数")

    # 获取玩家对象地址
    player_obj_addr = get_player_object_address(handle)
    if not player_obj_addr:
        print("❌ 无法获取玩家对象地址")
        return False

    print(f"� 玩家对象地址: 0x{player_obj_addr:08X}")

    # 构造移动命令数据
    success = call_movement_function(handle, player_obj_addr, target_x, target_y, direction)

    if success:
        print("✅ 移动函数调用成功!")

        # 等待移动完成
        time.sleep(1)

        # 验证坐标
        new_x, new_y = get_current_coordinates(handle)
        print(f"📍 移动后坐标: ({new_x}, {new_y})")

        # 检查是否接近目标位置
        distance_to_target = math.sqrt((new_x - target_x) ** 2 + (new_y - target_y) ** 2)
        if distance_to_target < 10:  # 允许10像素的误差
            print("🎉 移动成功!")
            return True
        else:
            print(f"⚠️ 移动未完全到达目标，距离: {distance_to_target:.1f}")
            return True  # 仍然算作成功，因为开始了移动
    else:
        print("❌ 移动函数调用失败")

    return False

def move_safe_mode(handle, target_x, target_y):
    """安全模式移动 (使用目标坐标地址)"""
    print(f"🛡️ 安全模式移动")

    current_x, current_y = get_current_coordinates(handle)
    print(f"📍 当前真实坐标: ({current_x}, {current_y})")

    # 设置目标坐标
    success = set_target_coordinates(handle, target_x, target_y)
    if not success:
        return False

    # 验证目标坐标设置
    check_x, check_y = get_target_coordinates(handle)
    print(f"🎯 目标坐标确认: ({check_x}, {check_y})")

    if check_x != target_x or check_y != target_y:
        print("❌ 目标坐标设置验证失败")
        return False

    # 获取移动控制基地址并设置移动状态
    movement_base = get_movement_control_address(handle)
    if movement_base:
        # 读取当前面朝方向
        current_facing = read_byte(handle, movement_base + 0x502)
        direction_names = ["上", "右上", "右", "右下", "下", "左下", "左", "左上"]
        print(f"📊 当前面朝方向: {current_facing} ({direction_names[current_facing]})")

        # 计算目标方向
        target_direction = calculate_direction_8way(current_x, current_y, target_x, target_y)
        print(f"🎯 目标方向: {target_direction} ({direction_names[target_direction]})")

        # 设置转向方向 (使用0x50E地址)
        write_byte(handle, movement_base + 0x50E, target_direction)
        print(f"✅ 转向方向已设置: {direction_names[target_direction]} (使用0x50E)")

        # 等待角色转向完成
        print(f"🔄 等待角色转向...")
        time.sleep(0.5)  # 等待500毫秒让角色转向

        # 验证转向是否完成 (检查0x50E地址)
        actual_facing = read_byte(handle, movement_base + 0x50E)
        if actual_facing == target_direction:
            print(f"✅ 转向完成: {direction_names[actual_facing]}")
        else:
            print(f"⚠️ 转向可能未完成: 期望{target_direction}, 实际{actual_facing}")
            # 再次尝试设置
            write_byte(handle, movement_base + 0x50E, target_direction)
            time.sleep(0.3)
            actual_facing = read_byte(handle, movement_base + 0x50E)
            print(f"🔄 二次尝试后: {actual_facing}")

        # 设置移动执行动作 (01=走, 02=跑, 03=战斗)
        write_byte(handle, movement_base + 0x50D, 1)  # 设置为行走状态
        print(f"✅ 移动执行动作已设置: 01 (走)")

        # 监控移动过程
        print(f"⏱️ 监控移动过程...")
        start_time = time.time()
        last_x, last_y = current_x, current_y
        last_direction_update = 0
        direction = target_direction  # 初始方向

        while time.time() - start_time < 15:  # 最多监控15秒
            new_x, new_y = get_current_coordinates(handle)
            current_time = time.time()

            # 每0.5秒更新一次移动方向 (使用0x50E)
            if current_time - last_direction_update >= 0.5:
                # 重新计算方向
                new_direction = calculate_direction_8way(new_x, new_y, target_x, target_y)

                # 如果方向变化了，更新移动中的方向
                if new_direction != direction:
                    # 先暂停移动
                    write_byte(handle, movement_base + 0x50D, 0)  # 暂停移动
                    time.sleep(0.1)  # 短暂暂停

                    # 更新方向
                    direction = new_direction
                    write_byte(handle, movement_base + 0x50E, direction)  # 使用移动中的方向地址
                    direction_names = ["上", "右上", "右", "右下", "下", "左下", "左", "左上"]
                    print(f"🧭 更新移动方向: {direction} ({direction_names[direction]})")

                    # 短暂延迟后恢复移动
                    time.sleep(0.1)
                    write_byte(handle, movement_base + 0x50D, 1)  # 恢复行走

                last_direction_update = current_time

            if new_x != last_x or new_y != last_y:
                distance_to_target = math.sqrt((new_x - target_x) ** 2 + (new_y - target_y) ** 2)
                print(f"📍 移动中: ({new_x}, {new_y}) 距离目标: {distance_to_target:.1f}")

                # 检查是否到达目标
                if distance_to_target < 5:
                    print("🎉 到达目标位置!")
                    # 停止移动 (00=待机)
                    write_byte(handle, movement_base + 0x50D, 0)  # 设置为待机状态
                    print("✅ 移动已停止 (00=待机)")
                    return True

                # 如果距离目标还很远但已经移动了一段时间，增加移动速度
                if distance_to_target > 20 and current_time - start_time > 3:
                    write_byte(handle, movement_base + 0x50D, 2)  # 设置为跑步状态 (02=跑)
                    print("🏃 增加移动速度为跑步 (02)")

                last_x, last_y = new_x, new_y

            time.sleep(0.2)

        # 超时，停止移动
        print("⏰ 移动超时，停止移动")
        write_byte(handle, movement_base + 0x50D, 0)  # 设置为待机状态 (00=待机)
        print("✅ 移动已停止 (00=待机)")

        final_x, final_y = get_current_coordinates(handle)
        final_distance = math.sqrt((final_x - target_x) ** 2 + (final_y - target_y) ** 2)
        print(f"📍 最终位置: ({final_x}, {final_y}) 距离目标: {final_distance:.1f}")

        return final_distance < 10
    else:
        print("❌ 无法获取移动控制基地址")
        return False

def calculate_direction_8way(from_x, from_y, to_x, to_y):
    """计算8方向 (0-7)"""
    dx = to_x - from_x
    dy = to_y - from_y

    if dx == 0 and dy == 0:
        return 0

    # 计算角度
    angle = math.atan2(dy, dx)
    degrees = math.degrees(angle)

    # 调整为0-360度
    if degrees < 0:
        degrees += 360

    # 转换为8方向
    # 0=上(270°), 1=右上(315°), 2=右(0°), 3=右下(45°), 4=下(90°), 5=左下(135°), 6=左(180°), 7=左上(225°)
    direction_angles = [270, 315, 0, 45, 90, 135, 180, 225]

    # 找到最接近的方向
    min_diff = 360
    best_direction = 0

    for i, target_angle in enumerate(direction_angles):
        diff = abs(degrees - target_angle)
        if diff > 180:
            diff = 360 - diff

        if diff < min_diff:
            min_diff = diff
            best_direction = i

    return best_direction

def write_byte(handle, address, value):
    """写入1字节"""
    try:
        buffer = struct.pack('B', value & 0xFF)
        bytes_written = ctypes.wintypes.DWORD()

        success = ctypes.windll.kernel32.WriteProcessMemory(
            handle, ctypes.c_void_p(address), buffer, 1, ctypes.byref(bytes_written)
        )

        return success and bytes_written.value == 1
    except:
        return False

def monitor_movement(handle, duration=10):
    """监控移动过程"""
    print(f"\n⏱️ 监控移动 ({duration}秒)...")
    print("时间  X坐标  Y坐标  变化")
    print("-" * 30)
    
    last_x, last_y = get_current_coordinates(handle)
    start_time = time.time()
    
    try:
        while time.time() - start_time < duration:
            current_x, current_y = get_current_coordinates(handle)
            
            if current_x != last_x or current_y != last_y:
                elapsed = int(time.time() - start_time)
                
                if last_x != 0 or last_y != 0:
                    distance = math.sqrt((current_x - last_x) ** 2 + (current_y - last_y) ** 2)
                    change_desc = f"移动 {distance:.1f}"
                else:
                    change_desc = "初始"
                
                print(f"{elapsed:3d}s  {current_x:4d}  {current_y:4d}  {change_desc}")
                
                last_x, last_y = current_x, current_y
            
            time.sleep(0.5)
            
    except KeyboardInterrupt:
        print("\n🛑 监控已停止")

def main():
    """主函数"""
    print("🎮 坐标移动工具")
    print("=" * 50)
    print("功能: 输入目标坐标，自动移动到指定位置")
    print()
    
    handle, pid = get_process_handle()
    if not handle:
        print("❌ 无法连接到游戏进程")
        return
    
    try:
        print(f"✅ 连接到进程 mts.exe (PID: {pid})")
        
        while True:
            # 显示当前坐标
            current_x, current_y = get_current_coordinates(handle)
            print(f"\n📍 当前坐标: ({current_x}, {current_y})")
            
            # 显示目标坐标
            target_x, target_y = get_target_coordinates(handle)
            print(f"🎯 目标坐标: ({target_x}, {target_y})")

            print("\n选择操作:")
            print("1. 移动到指定坐标")
            print("2. 监控坐标变化")
            print("3. 显示当前坐标")
            print("4. 显示目标坐标")
            print("0. 退出")

            choice = input("\n请选择 (0-4): ").strip()
            
            if choice == "1":
                try:
                    target_input = input("请输入目标坐标 (格式: x,y): ").strip()
                    target_x, target_y = map(int, target_input.split(','))
                    
                    print(f"\n🎯 目标坐标: ({target_x}, {target_y})")

                    # 显示目标坐标信息
                    target_x_check, target_y_check = get_target_coordinates(handle)
                    print(f"📊 当前目标坐标: ({target_x_check}, {target_y_check})")

                    # 安全检查
                    distance = math.sqrt((target_x - current_x) ** 2 + (target_y - current_y) ** 2)
                    if distance > 1000:
                        confirm = input(f"⚠️ 移动距离很远 ({distance:.1f})，确定继续? (y/n): ")
                        if confirm.lower() != 'y':
                            continue

                    # 选择移动方式
                    print(f"\n🔧 选择移动方式:")
                    print("1. 函数调用移动 (原方法)")
                    print("2. 安全模式移动 (使用目标坐标)")

                    move_choice = input("请选择移动方式 (1-2): ").strip()

                    if move_choice == "1":
                        # 原来的函数调用方法
                        success = move_to_coordinates(handle, target_x, target_y)
                    elif move_choice == "2":
                        # 新的安全模式方法
                        success = move_safe_mode(handle, target_x, target_y)
                    else:
                        print("❌ 无效选择")
                        continue
                    
                    if success:
                        # 监控移动结果
                        monitor_movement(handle, 5)
                    
                except ValueError:
                    print("❌ 坐标格式错误，请使用 x,y 格式")
                except Exception as e:
                    print(f"❌ 移动失败: {e}")
            
            elif choice == "2":
                monitor_movement(handle, 30)
            
            elif choice == "3":
                x, y = get_current_coordinates(handle)
                print(f"📍 当前真实坐标: ({x}, {y})")

            elif choice == "4":
                x, y = get_target_coordinates(handle)
                print(f"🎯 当前目标坐标: ({x}, {y})")

            elif choice == "0":
                print("👋 退出程序")
                break
            
            else:
                print("❌ 无效选择")
    
    finally:
        ctypes.windll.kernel32.CloseHandle(handle)

if __name__ == "__main__":
    main()
